# 用户注册登录系统

一个功能完整的HTML/CSS/JavaScript用户注册登录系统。

## 功能特性

### 🔐 用户认证
- 用户注册（用户名、邮箱、密码）
- 用户登录验证
- 记住登录状态
- 安全退出登录

### ✅ 表单验证
- 实时输入验证
- 用户名长度检查（3-20字符）
- 邮箱格式验证
- 密码强度检查（至少8位，包含字母和数字）
- 密码确认匹配
- 用户名和邮箱唯一性检查

### 🎨 用户界面
- 现代化响应式设计
- 平滑动画效果
- 密码可见性切换
- 友好的错误提示
- 移动端适配

### 💾 数据存储
- 使用localStorage存储用户数据
- 支持"记住我"功能
- 登录状态持久化

## 文件结构

```
├── index.html      # 主页面文件
├── style.css       # 样式文件
├── script.js       # JavaScript逻辑
└── README.md       # 说明文档
```

## 使用方法

1. 直接在浏览器中打开 `index.html` 文件
2. 首次使用请先注册新账户
3. 注册成功后可以登录系统
4. 登录后可以查看用户仪表板

## 技术栈

- **HTML5** - 页面结构
- **CSS3** - 样式和动画
- **JavaScript (ES6+)** - 交互逻辑
- **Font Awesome** - 图标库
- **localStorage** - 数据存储

## 安全说明

⚠️ **重要提示**：这是一个演示项目，仅用于学习和展示目的。在生产环境中，请注意以下安全事项：

1. **密码加密**：实际项目中应使用bcrypt等库对密码进行哈希加密
2. **数据库存储**：使用真实数据库替代localStorage
3. **HTTPS协议**：确保数据传输安全
4. **输入过滤**：防止XSS和SQL注入攻击
5. **会话管理**：实现安全的会话管理机制

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 自定义配置

### 修改密码规则
在 `script.js` 中修改 `isValidPassword` 函数：

```javascript
function isValidPassword(password) {
    // 自定义密码规则
    const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/;
    return passwordRegex.test(password);
}
```

### 修改样式主题
在 `style.css` 中修改CSS变量：

```css
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --error-color: #dc3545;
}
```

## 扩展功能建议

- [ ] 邮箱验证
- [ ] 忘记密码功能
- [ ] 用户头像上传
- [ ] 个人资料编辑
- [ ] 双因素认证
- [ ] 社交媒体登录
- [ ] 密码强度指示器
- [ ] 登录历史记录

## 许可证

MIT License - 可自由使用和修改

/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    margin: 0;
    box-sizing: border-box;
}

.container {
    position: relative;
    width: 100%;
    max-width: 380px;
    margin: 0 auto;
}

.form-container {
    background: rgba(255, 255, 255, 0.96);
    border-radius: 12px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    overflow: hidden;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.form-wrapper {
    padding: 30px 25px 25px 25px;
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.form-wrapper.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

h2 {
    text-align: center;
    margin-bottom: 25px;
    color: #333;
    font-size: 22px;
    font-weight: 600;
    letter-spacing: -0.3px;
}

h2 i {
    margin-right: 8px;
    color: #667eea;
    font-size: 20px;
}

.input-group {
    position: relative;
    margin-bottom: 16px;
}

.input-group i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    z-index: 2;
    font-size: 14px;
}

.input-group input {
    width: 100%;
    padding: 12px 40px 12px 35px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #fff;
    box-sizing: border-box;
}

.input-group input[type="password"],
.input-group input[type="text"] {
    padding-right: 40px;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.input-group input:valid {
    border-color: #28a745;
}

.input-group input:invalid:not(:placeholder-shown) {
    border-color: #dc3545;
}

.toggle-password {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #999;
    transition: color 0.3s ease;
    z-index: 3;
    font-size: 14px;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toggle-password:hover {
    color: #667eea;
}

.input-hint {
    display: block;
    font-size: 11px;
    color: #666;
    margin-top: 4px;
    margin-left: 2px;
    line-height: 1.2;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 10px;
    padding: 0;
}

.remember-me {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 13px;
    color: #666;
}

.remember-me input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 14px;
    height: 14px;
    border: 1px solid #ddd;
    border-radius: 2px;
    margin-right: 6px;
    position: relative;
    transition: all 0.3s ease;
}

.remember-me input[type="checkbox"]:checked + .checkmark {
    background: #667eea;
    border-color: #667eea;
}

.remember-me input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    color: white;
    font-size: 12px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.forgot-password, .terms-link, .privacy-link {
    color: #667eea;
    text-decoration: none;
    font-size: 13px;
    transition: color 0.3s ease;
}

.forgot-password:hover, .terms-link:hover, .privacy-link:hover {
    color: #5a6fd8;
    text-decoration: underline;
}

.btn {
    width: 100%;
    padding: 12px 18px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    margin-top: 10px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
    margin-bottom: 10px;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-2px);
}

.form-footer {
    text-align: center;
    margin-top: 18px;
    padding-top: 18px;
    border-top: 1px solid #e1e5e9;
}

.form-footer p {
    color: #666;
    font-size: 13px;
    margin: 0;
}

.form-footer a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
}

.form-footer a:hover {
    text-decoration: underline;
}

/* 用户仪表板样式 */
.dashboard {
    text-align: center;
}

.user-info {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
}

.avatar {
    margin-bottom: 20px;
}

.avatar i {
    font-size: 80px;
    color: #667eea;
}

.user-details h3 {
    color: #333;
    margin-bottom: 10px;
    font-size: 24px;
}

.user-details p {
    color: #666;
    margin-bottom: 5px;
}

.login-time {
    font-size: 14px;
    color: #999;
}

.dashboard-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* 消息提示样式 */
.message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 10px;
    color: white;
    font-weight: 600;
    z-index: 1000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    max-width: 300px;
}

.message.show {
    transform: translateX(0);
}

.message.success {
    background: #28a745;
}

.message.error {
    background: #dc3545;
}

.message.warning {
    background: #ffc107;
    color: #333;
}

/* 响应式设计 */
@media (max-width: 480px) {
    body {
        padding: 12px;
    }

    .container {
        max-width: 100%;
        margin: 0;
    }

    .form-container {
        border-radius: 12px;
    }

    .form-wrapper {
        padding: 28px 20px 24px 20px;
    }

    h2 {
        font-size: 22px;
        margin-bottom: 20px;
    }

    h2 i {
        font-size: 20px;
        margin-right: 6px;
    }

    .input-group {
        margin-bottom: 16px;
    }

    .input-group input {
        padding: 12px 40px 12px 35px;
        font-size: 16px;
        border-radius: 6px;
    }

    .input-group i {
        left: 11px;
        font-size: 14px;
    }

    .toggle-password {
        right: 11px;
        font-size: 14px;
        width: 16px;
        height: 16px;
    }

    .input-hint {
        font-size: 11px;
        margin-top: 4px;
        margin-left: 3px;
    }

    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
        margin-bottom: 20px;
    }

    .checkmark {
        width: 14px;
        height: 14px;
        margin-right: 6px;
    }

    .remember-me {
        font-size: 13px;
    }

    .forgot-password {
        font-size: 13px;
    }

    .dashboard-actions {
        flex-direction: column;
    }

    .btn {
        padding: 12px 16px;
        font-size: 15px;
        border-radius: 6px;
        margin-top: 6px;
    }

    .form-footer {
        margin-top: 16px;
        padding-top: 16px;
    }

    .form-footer p {
        font-size: 13px;
    }
}

    /* 模态框响应式 */
    .modal {
        width: 95vw;
        max-height: 95vh;
        margin: 10px;
    }

    .modal-header {
        padding: 20px;
    }

    .modal-header h2 {
        font-size: 20px;
    }

    .modal-body {
        padding: 20px;
        max-height: 70vh;
    }

    .modal-footer {
        padding: 15px 20px;
        flex-direction: column;
    }

    .modal-footer .btn {
        width: 100%;
        margin-bottom: 10px;
    }

    .modal-footer .btn:last-child {
        margin-bottom: 0;
    }

    .terms-content h3,
    .privacy-content h3 {
        font-size: 16px;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 1000;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal-overlay.show {
    display: block;
    opacity: 1;
}

.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.7);
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    z-index: 1001;
    display: none;
    max-width: 90vw;
    max-height: 90vh;
    width: 600px;
    opacity: 0;
    transition: all 0.3s ease;
}

.modal.show {
    display: block;
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}

.modal-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 1px solid #e1e5e9;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
}

.modal-header h2 {
    margin: 0;
    font-size: 24px;
    color: white;
}

.modal-header h2 i {
    margin-right: 10px;
    color: rgba(255, 255, 255, 0.9);
}

.close-btn {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.8);
    transition: color 0.3s ease;
    line-height: 1;
    padding: 0;
    background: none;
    border: none;
}

.close-btn:hover {
    color: white;
}

.modal-body {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
    max-height: 60vh;
}

.modal-body::-webkit-scrollbar {
    width: 8px;
}

.modal-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.terms-content h3,
.privacy-content h3 {
    color: #333;
    margin: 25px 0 15px 0;
    font-size: 18px;
    font-weight: 600;
    border-left: 4px solid #667eea;
    padding-left: 15px;
}

.terms-content h3:first-child,
.privacy-content h3:first-child {
    margin-top: 0;
}

.terms-content p,
.privacy-content p {
    color: #555;
    line-height: 1.6;
    margin-bottom: 15px;
    text-align: justify;
}

.terms-content ul,
.privacy-content ul {
    margin: 15px 0;
    padding-left: 25px;
}

.terms-content li,
.privacy-content li {
    color: #555;
    line-height: 1.6;
    margin-bottom: 8px;
}

.terms-content strong,
.privacy-content strong {
    color: #333;
    font-weight: 600;
}

.last-updated {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
    margin-top: 30px;
    font-style: italic;
    color: #666;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    padding: 20px 30px;
    border-top: 1px solid #e1e5e9;
    background: #f8f9fa;
    border-radius: 0 0 15px 15px;
}

.modal-footer .btn {
    width: auto;
    padding: 12px 25px;
    min-width: 100px;
}

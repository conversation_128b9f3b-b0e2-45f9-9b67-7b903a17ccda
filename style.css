/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.container {
    position: relative;
    width: 100%;
    max-width: 450px;
}

.form-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    overflow: hidden;
    position: relative;
}

.form-wrapper {
    padding: 40px;
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.form-wrapper.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
    font-size: 28px;
    font-weight: 600;
}

h2 i {
    margin-right: 10px;
    color: #667eea;
}

.input-group {
    position: relative;
    margin-bottom: 25px;
}

.input-group i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    z-index: 1;
}

.input-group input {
    width: 100%;
    padding: 15px 15px 15px 45px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-group input:valid {
    border-color: #28a745;
}

.input-group input:invalid:not(:placeholder-shown) {
    border-color: #dc3545;
}

.toggle-password {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #999;
    transition: color 0.3s ease;
}

.toggle-password:hover {
    color: #667eea;
}

.input-hint {
    display: block;
    font-size: 12px;
    color: #666;
    margin-top: 5px;
    margin-left: 5px;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 10px;
}

.remember-me {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: #666;
}

.remember-me input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #ddd;
    border-radius: 4px;
    margin-right: 8px;
    position: relative;
    transition: all 0.3s ease;
}

.remember-me input[type="checkbox"]:checked + .checkmark {
    background: #667eea;
    border-color: #667eea;
}

.remember-me input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    color: white;
    font-size: 12px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.forgot-password, .terms-link, .privacy-link {
    color: #667eea;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.forgot-password:hover, .terms-link:hover, .privacy-link:hover {
    color: #5a6fd8;
    text-decoration: underline;
}

.btn {
    width: 100%;
    padding: 15px;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
    margin-bottom: 10px;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-2px);
}

.form-footer {
    text-align: center;
    margin-top: 25px;
    padding-top: 25px;
    border-top: 1px solid #e1e5e9;
}

.form-footer p {
    color: #666;
    font-size: 14px;
}

.form-footer a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
}

.form-footer a:hover {
    text-decoration: underline;
}

/* 用户仪表板样式 */
.dashboard {
    text-align: center;
}

.user-info {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
}

.avatar {
    margin-bottom: 20px;
}

.avatar i {
    font-size: 80px;
    color: #667eea;
}

.user-details h3 {
    color: #333;
    margin-bottom: 10px;
    font-size: 24px;
}

.user-details p {
    color: #666;
    margin-bottom: 5px;
}

.login-time {
    font-size: 14px;
    color: #999;
}

.dashboard-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* 消息提示样式 */
.message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 10px;
    color: white;
    font-weight: 600;
    z-index: 1000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    max-width: 300px;
}

.message.show {
    transform: translateX(0);
}

.message.success {
    background: #28a745;
}

.message.error {
    background: #dc3545;
}

.message.warning {
    background: #ffc107;
    color: #333;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .container {
        padding: 10px;
    }

    .form-wrapper {
        padding: 30px 25px;
    }

    h2 {
        font-size: 24px;
    }

    .form-options {
        flex-direction: column;
        align-items: flex-start;
    }

    .dashboard-actions {
        flex-direction: column;
    }

    /* 模态框响应式 */
    .modal {
        width: 95vw;
        max-height: 95vh;
        margin: 10px;
    }

    .modal-header {
        padding: 20px;
    }

    .modal-header h2 {
        font-size: 20px;
    }

    .modal-body {
        padding: 20px;
        max-height: 70vh;
    }

    .modal-footer {
        padding: 15px 20px;
        flex-direction: column;
    }

    .modal-footer .btn {
        width: 100%;
        margin-bottom: 10px;
    }

    .modal-footer .btn:last-child {
        margin-bottom: 0;
    }

    .terms-content h3,
    .privacy-content h3 {
        font-size: 16px;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 1000;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal-overlay.show {
    display: block;
    opacity: 1;
}

.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.7);
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    z-index: 1001;
    display: none;
    max-width: 90vw;
    max-height: 90vh;
    width: 600px;
    opacity: 0;
    transition: all 0.3s ease;
}

.modal.show {
    display: block;
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}

.modal-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 1px solid #e1e5e9;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
}

.modal-header h2 {
    margin: 0;
    font-size: 24px;
    color: white;
}

.modal-header h2 i {
    margin-right: 10px;
    color: rgba(255, 255, 255, 0.9);
}

.close-btn {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.8);
    transition: color 0.3s ease;
    line-height: 1;
    padding: 0;
    background: none;
    border: none;
}

.close-btn:hover {
    color: white;
}

.modal-body {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
    max-height: 60vh;
}

.modal-body::-webkit-scrollbar {
    width: 8px;
}

.modal-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.terms-content h3,
.privacy-content h3 {
    color: #333;
    margin: 25px 0 15px 0;
    font-size: 18px;
    font-weight: 600;
    border-left: 4px solid #667eea;
    padding-left: 15px;
}

.terms-content h3:first-child,
.privacy-content h3:first-child {
    margin-top: 0;
}

.terms-content p,
.privacy-content p {
    color: #555;
    line-height: 1.6;
    margin-bottom: 15px;
    text-align: justify;
}

.terms-content ul,
.privacy-content ul {
    margin: 15px 0;
    padding-left: 25px;
}

.terms-content li,
.privacy-content li {
    color: #555;
    line-height: 1.6;
    margin-bottom: 8px;
}

.terms-content strong,
.privacy-content strong {
    color: #333;
    font-weight: 600;
}

.last-updated {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
    margin-top: 30px;
    font-style: italic;
    color: #666;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    padding: 20px 30px;
    border-top: 1px solid #e1e5e9;
    background: #f8f9fa;
    border-radius: 0 0 15px 15px;
}

.modal-footer .btn {
    width: auto;
    padding: 12px 25px;
    min-width: 100px;
}

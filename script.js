// 用户数据存储（实际项目中应使用数据库）
let users = JSON.parse(localStorage.getItem('users')) || [];
let currentUser = JSON.parse(localStorage.getItem('currentUser')) || null;
let resetCodes = JSON.parse(localStorage.getItem('resetCodes')) || {};

// 初始化演示用户（仅用于测试）
function initializeDemoUser() {
    if (users.length === 0) {
        const demoUser = {
            id: 1,
            username: 'demo',
            email: '<EMAIL>',
            password: 'demo123456',
            registeredAt: new Date().toISOString()
        };
        users.push(demoUser);
        localStorage.setItem('users', JSON.stringify(users));
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeDemoUser();
    initializeApp();
    setupEventListeners();
});

// 初始化应用
function initializeApp() {
    if (currentUser) {
        showDashboard();
    } else {
        showLoginForm();
    }
}

// 设置事件监听器
function setupEventListeners() {
    // 登录表单提交
    document.getElementById('loginFormElement').addEventListener('submit', handleLogin);

    // 注册表单提交
    document.getElementById('registerFormElement').addEventListener('submit', handleRegister);

    // 忘记密码表单提交
    document.getElementById('forgotFormElement').addEventListener('submit', handleForgotPassword);

    // 密码重置表单提交
    document.getElementById('resetFormElement').addEventListener('submit', handlePasswordReset);

    // 密码强度检查
    document.getElementById('registerPassword').addEventListener('input', checkPasswordStrength);

    // 确认密码检查
    document.getElementById('confirmPassword').addEventListener('input', checkPasswordMatch);

    // 新密码强度检查
    document.getElementById('newPassword').addEventListener('input', checkNewPasswordStrength);

    // 确认新密码检查
    document.getElementById('confirmNewPassword').addEventListener('input', checkNewPasswordMatch);

    // 用户名可用性检查
    document.getElementById('registerUsername').addEventListener('blur', checkUsernameAvailability);

    // 邮箱格式检查
    document.getElementById('registerEmail').addEventListener('blur', checkEmailFormat);
}

// 切换表单
function switchForm(formType) {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    const forgotForm = document.getElementById('forgotForm');
    const resetForm = document.getElementById('resetForm');
    const dashboard = document.getElementById('dashboard');

    // 隐藏所有表单
    loginForm.classList.remove('active');
    registerForm.classList.remove('active');
    forgotForm.classList.remove('active');
    resetForm.classList.remove('active');
    dashboard.classList.remove('active');

    // 显示指定表单
    if (formType === 'login') {
        loginForm.classList.add('active');
    } else if (formType === 'register') {
        registerForm.classList.add('active');
    } else if (formType === 'forgot') {
        forgotForm.classList.add('active');
    } else if (formType === 'reset') {
        resetForm.classList.add('active');
    }

    // 清除表单数据
    clearForms();
}

// 显示登录表单
function showLoginForm() {
    switchForm('login');
}

// 显示注册表单
function showRegisterForm() {
    switchForm('register');
}

// 显示用户仪表板
function showDashboard() {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    const forgotForm = document.getElementById('forgotForm');
    const resetForm = document.getElementById('resetForm');
    const dashboard = document.getElementById('dashboard');

    loginForm.classList.remove('active');
    registerForm.classList.remove('active');
    forgotForm.classList.remove('active');
    resetForm.classList.remove('active');
    dashboard.classList.add('active');

    // 更新用户信息
    document.getElementById('welcomeUser').textContent = `欢迎回来，${currentUser.username}！`;
    document.getElementById('userEmail').textContent = currentUser.email;
    document.getElementById('loginTime').textContent = new Date().toLocaleString();
}

// 处理登录
function handleLogin(e) {
    e.preventDefault();
    
    const username = document.getElementById('loginUsername').value.trim();
    const password = document.getElementById('loginPassword').value;
    const rememberMe = document.getElementById('rememberMe').checked;
    
    // 验证输入
    if (!username || !password) {
        showMessage('请填写所有必填字段', 'error');
        return;
    }
    
    // 查找用户
    const user = users.find(u => 
        (u.username === username || u.email === username) && u.password === password
    );
    
    if (user) {
        currentUser = user;
        
        // 保存登录状态
        if (rememberMe) {
            localStorage.setItem('currentUser', JSON.stringify(currentUser));
        } else {
            sessionStorage.setItem('currentUser', JSON.stringify(currentUser));
        }
        
        showMessage('登录成功！', 'success');
        setTimeout(() => {
            showDashboard();
        }, 1000);
    } else {
        showMessage('用户名或密码错误', 'error');
    }
}

// 处理注册
function handleRegister(e) {
    e.preventDefault();
    
    const username = document.getElementById('registerUsername').value.trim();
    const email = document.getElementById('registerEmail').value.trim();
    const password = document.getElementById('registerPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const agreeTerms = document.getElementById('agreeTerms').checked;
    
    // 验证输入
    if (!validateRegistration(username, email, password, confirmPassword, agreeTerms)) {
        return;
    }
    
    // 检查用户名是否已存在
    if (users.some(u => u.username === username)) {
        showMessage('用户名已存在', 'error');
        return;
    }
    
    // 检查邮箱是否已存在
    if (users.some(u => u.email === email)) {
        showMessage('邮箱已被注册', 'error');
        return;
    }
    
    // 创建新用户
    const newUser = {
        id: Date.now(),
        username: username,
        email: email,
        password: password,
        registeredAt: new Date().toISOString()
    };
    
    users.push(newUser);
    localStorage.setItem('users', JSON.stringify(users));
    
    showMessage('注册成功！请登录', 'success');
    setTimeout(() => {
        switchForm('login');
        // 自动填充用户名
        document.getElementById('loginUsername').value = username;
    }, 1000);
}

// 处理忘记密码
function handleForgotPassword(e) {
    e.preventDefault();

    const email = document.getElementById('forgotEmail').value.trim();

    // 验证邮箱格式
    if (!email) {
        showMessage('请输入邮箱地址', 'error');
        return;
    }

    if (!isValidEmail(email)) {
        showMessage('请输入有效的邮箱地址', 'error');
        return;
    }

    // 检查邮箱是否已注册
    const user = users.find(u => u.email === email);
    if (!user) {
        showMessage('该邮箱未注册', 'error');
        return;
    }

    // 生成6位验证码
    const resetCode = generateResetCode();

    // 保存验证码（实际项目中应该发送邮件）
    resetCodes[email] = {
        code: resetCode,
        timestamp: Date.now(),
        attempts: 0
    };
    localStorage.setItem('resetCodes', JSON.stringify(resetCodes));

    // 模拟发送邮件
    showMessage(`验证码已发送到 ${email}（模拟：${resetCode}）`, 'success');

    // 切换到重置密码表单
    setTimeout(() => {
        switchForm('reset');
        document.getElementById('resetCode').focus();
    }, 2000);
}

// 处理密码重置
function handlePasswordReset(e) {
    e.preventDefault();

    const code = document.getElementById('resetCode').value.trim();
    const newPassword = document.getElementById('newPassword').value;
    const confirmNewPassword = document.getElementById('confirmNewPassword').value;

    // 验证输入
    if (!code || !newPassword || !confirmNewPassword) {
        showMessage('请填写所有字段', 'error');
        return;
    }

    // 验证验证码
    const email = findEmailByCode(code);
    if (!email) {
        showMessage('验证码无效或已过期', 'error');
        return;
    }

    // 验证新密码
    if (!isValidPassword(newPassword)) {
        showMessage('密码至少8位，包含字母和数字', 'error');
        return;
    }

    if (newPassword !== confirmNewPassword) {
        showMessage('两次输入的密码不一致', 'error');
        return;
    }

    // 更新用户密码
    const user = users.find(u => u.email === email);
    if (user) {
        user.password = newPassword;
        localStorage.setItem('users', JSON.stringify(users));

        // 清除验证码
        delete resetCodes[email];
        localStorage.setItem('resetCodes', JSON.stringify(resetCodes));

        showMessage('密码重置成功！请使用新密码登录', 'success');
        setTimeout(() => {
            switchForm('login');
            document.getElementById('loginUsername').value = user.username;
        }, 2000);
    }
}

// 验证注册信息
function validateRegistration(username, email, password, confirmPassword, agreeTerms) {
    if (!username || !email || !password || !confirmPassword) {
        showMessage('请填写所有必填字段', 'error');
        return false;
    }
    
    if (username.length < 3 || username.length > 20) {
        showMessage('用户名长度应为3-20个字符', 'error');
        return false;
    }
    
    if (!isValidEmail(email)) {
        showMessage('请输入有效的邮箱地址', 'error');
        return false;
    }
    
    if (!isValidPassword(password)) {
        showMessage('密码至少8位，包含字母和数字', 'error');
        return false;
    }
    
    if (password !== confirmPassword) {
        showMessage('两次输入的密码不一致', 'error');
        return false;
    }
    
    if (!agreeTerms) {
        showMessage('请同意用户协议和隐私政策', 'error');
        return false;
    }
    
    return true;
}

// 检查邮箱格式
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// 检查密码强度
function isValidPassword(password) {
    const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/;
    return passwordRegex.test(password);
}

// 生成重置验证码
function generateResetCode() {
    return Math.floor(100000 + Math.random() * 900000).toString();
}

// 根据验证码查找邮箱
function findEmailByCode(code) {
    for (const email in resetCodes) {
        const resetData = resetCodes[email];
        if (resetData.code === code) {
            // 检查验证码是否过期（15分钟）
            const now = Date.now();
            const codeAge = now - resetData.timestamp;
            if (codeAge > 15 * 60 * 1000) {
                delete resetCodes[email];
                localStorage.setItem('resetCodes', JSON.stringify(resetCodes));
                return null;
            }
            return email;
        }
    }
    return null;
}

// 重新发送验证码
function resendCode() {
    const email = document.getElementById('forgotEmail').value.trim();
    if (!email) {
        showMessage('请先在忘记密码页面输入邮箱', 'error');
        switchForm('forgot');
        return;
    }

    // 检查是否存在该邮箱的验证码记录
    if (resetCodes[email]) {
        const lastSent = resetCodes[email].timestamp;
        const now = Date.now();
        const timeDiff = now - lastSent;

        // 限制重发频率（1分钟）
        if (timeDiff < 60 * 1000) {
            const remainingTime = Math.ceil((60 * 1000 - timeDiff) / 1000);
            showMessage(`请等待 ${remainingTime} 秒后再重新发送`, 'warning');
            return;
        }
    }

    // 生成新验证码
    const resetCode = generateResetCode();
    resetCodes[email] = {
        code: resetCode,
        timestamp: Date.now(),
        attempts: 0
    };
    localStorage.setItem('resetCodes', JSON.stringify(resetCodes));

    showMessage(`新验证码已发送到 ${email}（模拟：${resetCode}）`, 'success');
}

// 检查密码强度（实时）
function checkPasswordStrength() {
    const password = document.getElementById('registerPassword').value;
    const hint = document.querySelector('#registerPassword').nextElementSibling.nextElementSibling;

    if (password.length === 0) {
        hint.textContent = '密码至少8位，包含字母和数字';
        hint.style.color = '#666';
    } else if (isValidPassword(password)) {
        hint.textContent = '密码强度：强';
        hint.style.color = '#28a745';
    } else if (password.length >= 8) {
        hint.textContent = '密码强度：中等（建议包含字母和数字）';
        hint.style.color = '#ffc107';
    } else {
        hint.textContent = '密码强度：弱（至少8位字符）';
        hint.style.color = '#dc3545';
    }
}

// 检查新密码强度（实时）
function checkNewPasswordStrength() {
    const password = document.getElementById('newPassword').value;
    const hint = document.querySelector('#newPassword').nextElementSibling.nextElementSibling;

    if (password.length === 0) {
        hint.textContent = '密码至少8位，包含字母和数字';
        hint.style.color = '#666';
    } else if (isValidPassword(password)) {
        hint.textContent = '密码强度：强';
        hint.style.color = '#28a745';
    } else if (password.length >= 8) {
        hint.textContent = '密码强度：中等（建议包含字母和数字）';
        hint.style.color = '#ffc107';
    } else {
        hint.textContent = '密码强度：弱（至少8位字符）';
        hint.style.color = '#dc3545';
    }
}

// 检查密码匹配
function checkPasswordMatch() {
    const password = document.getElementById('registerPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const confirmInput = document.getElementById('confirmPassword');

    if (confirmPassword.length === 0) {
        confirmInput.style.borderColor = '#e1e5e9';
    } else if (password === confirmPassword) {
        confirmInput.style.borderColor = '#28a745';
    } else {
        confirmInput.style.borderColor = '#dc3545';
    }
}

// 检查新密码匹配
function checkNewPasswordMatch() {
    const password = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmNewPassword').value;
    const confirmInput = document.getElementById('confirmNewPassword');

    if (confirmPassword.length === 0) {
        confirmInput.style.borderColor = '#e1e5e9';
    } else if (password === confirmPassword) {
        confirmInput.style.borderColor = '#28a745';
    } else {
        confirmInput.style.borderColor = '#dc3545';
    }
}

// 检查用户名可用性
function checkUsernameAvailability() {
    const username = document.getElementById('registerUsername').value.trim();
    const input = document.getElementById('registerUsername');
    
    if (username.length === 0) {
        input.style.borderColor = '#e1e5e9';
        return;
    }
    
    if (users.some(u => u.username === username)) {
        input.style.borderColor = '#dc3545';
        showMessage('用户名已存在', 'warning');
    } else if (username.length >= 3 && username.length <= 20) {
        input.style.borderColor = '#28a745';
    }
}

// 检查邮箱格式
function checkEmailFormat() {
    const email = document.getElementById('registerEmail').value.trim();
    const input = document.getElementById('registerEmail');
    
    if (email.length === 0) {
        input.style.borderColor = '#e1e5e9';
        return;
    }
    
    if (isValidEmail(email)) {
        if (users.some(u => u.email === email)) {
            input.style.borderColor = '#dc3545';
            showMessage('邮箱已被注册', 'warning');
        } else {
            input.style.borderColor = '#28a745';
        }
    } else {
        input.style.borderColor = '#dc3545';
    }
}

// 切换密码可见性
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const icon = input.nextElementSibling.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// 显示消息
function showMessage(text, type) {
    const message = document.getElementById('message');
    message.textContent = text;
    message.className = `message ${type}`;
    message.classList.add('show');
    
    setTimeout(() => {
        message.classList.remove('show');
    }, 3000);
}

// 清除表单
function clearForms() {
    document.getElementById('loginFormElement').reset();
    document.getElementById('registerFormElement').reset();
    document.getElementById('forgotFormElement').reset();
    document.getElementById('resetFormElement').reset();

    // 重置输入框边框颜色
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.style.borderColor = '#e1e5e9';
    });

    // 重置密码提示文本
    const hints = document.querySelectorAll('.input-hint');
    hints.forEach(hint => {
        if (hint.textContent.includes('密码强度')) {
            hint.textContent = '密码至少8位，包含字母和数字';
            hint.style.color = '#666';
        }
    });
}

// 编辑资料
function editProfile() {
    showMessage('编辑资料功能开发中...', 'warning');
}

// 退出登录
function logout() {
    currentUser = null;
    localStorage.removeItem('currentUser');
    sessionStorage.removeItem('currentUser');
    showMessage('已退出登录', 'success');
    setTimeout(() => {
        showLoginForm();
    }, 1000);
}

// 显示模态框
function showModal(type) {
    const overlay = document.getElementById('modalOverlay');
    const modal = document.getElementById(type === 'terms' ? 'termsModal' : 'privacyModal');

    overlay.style.display = 'block';
    modal.style.display = 'block';

    // 添加动画效果
    setTimeout(() => {
        overlay.classList.add('show');
        modal.classList.add('show');
    }, 10);

    // 防止背景滚动
    document.body.style.overflow = 'hidden';
}

// 关闭模态框
function closeModal() {
    const overlay = document.getElementById('modalOverlay');
    const termsModal = document.getElementById('termsModal');
    const privacyModal = document.getElementById('privacyModal');

    overlay.classList.remove('show');
    termsModal.classList.remove('show');
    privacyModal.classList.remove('show');

    // 恢复背景滚动
    document.body.style.overflow = 'auto';

    // 延迟隐藏元素
    setTimeout(() => {
        overlay.style.display = 'none';
        termsModal.style.display = 'none';
        privacyModal.style.display = 'none';
    }, 300);
}

// 接受用户协议
function acceptTerms() {
    const agreeTermsCheckbox = document.getElementById('agreeTerms');
    if (agreeTermsCheckbox) {
        agreeTermsCheckbox.checked = true;
        showMessage('已同意用户协议', 'success');
    }
    closeModal();
}

// 接受隐私政策
function acceptPrivacy() {
    const agreeTermsCheckbox = document.getElementById('agreeTerms');
    if (agreeTermsCheckbox) {
        agreeTermsCheckbox.checked = true;
        showMessage('已同意隐私政策', 'success');
    }
    closeModal();
}

// 键盘事件处理
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeModal();
    }
});

// 防止模态框内容区域点击时关闭模态框
document.addEventListener('DOMContentLoaded', function() {
    const modals = document.querySelectorAll('.modal-content');
    modals.forEach(modal => {
        modal.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    });
});

// 用户数据存储（实际项目中应使用数据库）
let users = JSON.parse(localStorage.getItem('users')) || [];
let currentUser = JSON.parse(localStorage.getItem('currentUser')) || null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
});

// 初始化应用
function initializeApp() {
    if (currentUser) {
        showDashboard();
    } else {
        showLoginForm();
    }
}

// 设置事件监听器
function setupEventListeners() {
    // 登录表单提交
    document.getElementById('loginFormElement').addEventListener('submit', handleLogin);
    
    // 注册表单提交
    document.getElementById('registerFormElement').addEventListener('submit', handleRegister);
    
    // 密码强度检查
    document.getElementById('registerPassword').addEventListener('input', checkPasswordStrength);
    
    // 确认密码检查
    document.getElementById('confirmPassword').addEventListener('input', checkPasswordMatch);
    
    // 用户名可用性检查
    document.getElementById('registerUsername').addEventListener('blur', checkUsernameAvailability);
    
    // 邮箱格式检查
    document.getElementById('registerEmail').addEventListener('blur', checkEmailFormat);
}

// 切换表单
function switchForm(formType) {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    const dashboard = document.getElementById('dashboard');
    
    // 隐藏所有表单
    loginForm.classList.remove('active');
    registerForm.classList.remove('active');
    dashboard.classList.remove('active');
    
    // 显示指定表单
    if (formType === 'login') {
        loginForm.classList.add('active');
    } else if (formType === 'register') {
        registerForm.classList.add('active');
    }
    
    // 清除表单数据
    clearForms();
}

// 显示登录表单
function showLoginForm() {
    switchForm('login');
}

// 显示注册表单
function showRegisterForm() {
    switchForm('register');
}

// 显示用户仪表板
function showDashboard() {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    const dashboard = document.getElementById('dashboard');
    
    loginForm.classList.remove('active');
    registerForm.classList.remove('active');
    dashboard.classList.add('active');
    
    // 更新用户信息
    document.getElementById('welcomeUser').textContent = `欢迎回来，${currentUser.username}！`;
    document.getElementById('userEmail').textContent = currentUser.email;
    document.getElementById('loginTime').textContent = new Date().toLocaleString();
}

// 处理登录
function handleLogin(e) {
    e.preventDefault();
    
    const username = document.getElementById('loginUsername').value.trim();
    const password = document.getElementById('loginPassword').value;
    const rememberMe = document.getElementById('rememberMe').checked;
    
    // 验证输入
    if (!username || !password) {
        showMessage('请填写所有必填字段', 'error');
        return;
    }
    
    // 查找用户
    const user = users.find(u => 
        (u.username === username || u.email === username) && u.password === password
    );
    
    if (user) {
        currentUser = user;
        
        // 保存登录状态
        if (rememberMe) {
            localStorage.setItem('currentUser', JSON.stringify(currentUser));
        } else {
            sessionStorage.setItem('currentUser', JSON.stringify(currentUser));
        }
        
        showMessage('登录成功！', 'success');
        setTimeout(() => {
            showDashboard();
        }, 1000);
    } else {
        showMessage('用户名或密码错误', 'error');
    }
}

// 处理注册
function handleRegister(e) {
    e.preventDefault();
    
    const username = document.getElementById('registerUsername').value.trim();
    const email = document.getElementById('registerEmail').value.trim();
    const password = document.getElementById('registerPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const agreeTerms = document.getElementById('agreeTerms').checked;
    
    // 验证输入
    if (!validateRegistration(username, email, password, confirmPassword, agreeTerms)) {
        return;
    }
    
    // 检查用户名是否已存在
    if (users.some(u => u.username === username)) {
        showMessage('用户名已存在', 'error');
        return;
    }
    
    // 检查邮箱是否已存在
    if (users.some(u => u.email === email)) {
        showMessage('邮箱已被注册', 'error');
        return;
    }
    
    // 创建新用户
    const newUser = {
        id: Date.now(),
        username: username,
        email: email,
        password: password,
        registeredAt: new Date().toISOString()
    };
    
    users.push(newUser);
    localStorage.setItem('users', JSON.stringify(users));
    
    showMessage('注册成功！请登录', 'success');
    setTimeout(() => {
        switchForm('login');
        // 自动填充用户名
        document.getElementById('loginUsername').value = username;
    }, 1000);
}

// 验证注册信息
function validateRegistration(username, email, password, confirmPassword, agreeTerms) {
    if (!username || !email || !password || !confirmPassword) {
        showMessage('请填写所有必填字段', 'error');
        return false;
    }
    
    if (username.length < 3 || username.length > 20) {
        showMessage('用户名长度应为3-20个字符', 'error');
        return false;
    }
    
    if (!isValidEmail(email)) {
        showMessage('请输入有效的邮箱地址', 'error');
        return false;
    }
    
    if (!isValidPassword(password)) {
        showMessage('密码至少8位，包含字母和数字', 'error');
        return false;
    }
    
    if (password !== confirmPassword) {
        showMessage('两次输入的密码不一致', 'error');
        return false;
    }
    
    if (!agreeTerms) {
        showMessage('请同意用户协议和隐私政策', 'error');
        return false;
    }
    
    return true;
}

// 检查邮箱格式
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// 检查密码强度
function isValidPassword(password) {
    const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/;
    return passwordRegex.test(password);
}

// 检查密码强度（实时）
function checkPasswordStrength() {
    const password = document.getElementById('registerPassword').value;
    const hint = document.querySelector('#registerPassword').nextElementSibling.nextElementSibling;
    
    if (password.length === 0) {
        hint.textContent = '密码至少8位，包含字母和数字';
        hint.style.color = '#666';
    } else if (isValidPassword(password)) {
        hint.textContent = '密码强度：强';
        hint.style.color = '#28a745';
    } else if (password.length >= 8) {
        hint.textContent = '密码强度：中等（建议包含字母和数字）';
        hint.style.color = '#ffc107';
    } else {
        hint.textContent = '密码强度：弱（至少8位字符）';
        hint.style.color = '#dc3545';
    }
}

// 检查密码匹配
function checkPasswordMatch() {
    const password = document.getElementById('registerPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const confirmInput = document.getElementById('confirmPassword');
    
    if (confirmPassword.length === 0) {
        confirmInput.style.borderColor = '#e1e5e9';
    } else if (password === confirmPassword) {
        confirmInput.style.borderColor = '#28a745';
    } else {
        confirmInput.style.borderColor = '#dc3545';
    }
}

// 检查用户名可用性
function checkUsernameAvailability() {
    const username = document.getElementById('registerUsername').value.trim();
    const input = document.getElementById('registerUsername');
    
    if (username.length === 0) {
        input.style.borderColor = '#e1e5e9';
        return;
    }
    
    if (users.some(u => u.username === username)) {
        input.style.borderColor = '#dc3545';
        showMessage('用户名已存在', 'warning');
    } else if (username.length >= 3 && username.length <= 20) {
        input.style.borderColor = '#28a745';
    }
}

// 检查邮箱格式
function checkEmailFormat() {
    const email = document.getElementById('registerEmail').value.trim();
    const input = document.getElementById('registerEmail');
    
    if (email.length === 0) {
        input.style.borderColor = '#e1e5e9';
        return;
    }
    
    if (isValidEmail(email)) {
        if (users.some(u => u.email === email)) {
            input.style.borderColor = '#dc3545';
            showMessage('邮箱已被注册', 'warning');
        } else {
            input.style.borderColor = '#28a745';
        }
    } else {
        input.style.borderColor = '#dc3545';
    }
}

// 切换密码可见性
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const icon = input.nextElementSibling.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// 显示消息
function showMessage(text, type) {
    const message = document.getElementById('message');
    message.textContent = text;
    message.className = `message ${type}`;
    message.classList.add('show');
    
    setTimeout(() => {
        message.classList.remove('show');
    }, 3000);
}

// 清除表单
function clearForms() {
    document.getElementById('loginFormElement').reset();
    document.getElementById('registerFormElement').reset();
    
    // 重置输入框边框颜色
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.style.borderColor = '#e1e5e9';
    });
}

// 编辑资料
function editProfile() {
    showMessage('编辑资料功能开发中...', 'warning');
}

// 退出登录
function logout() {
    currentUser = null;
    localStorage.removeItem('currentUser');
    sessionStorage.removeItem('currentUser');
    showMessage('已退出登录', 'success');
    setTimeout(() => {
        showLoginForm();
    }, 1000);
}
